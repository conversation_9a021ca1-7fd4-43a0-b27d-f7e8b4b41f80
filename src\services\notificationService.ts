import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  onSnapshot,
  setDoc,
  Timestamp
} from 'firebase/firestore';
import { db } from './api';
import {
  DiagnosticEvent,
  CreateDiagnosticEventData,
  UpdateDiagnosticEventData,
  UserNotification,
  CreateNotificationData,
  UserActionHistory,
  CreateActionHistoryData,
  NotificationStats,
  DiagnosticEventFilters,
  NotificationPriority,
  DiagnosticEventStatus
} from '../types/notifications';
import { DiagnosticRecord, GeminiDiagnosis } from '../types';

/**
 * Service de gestion des notifications et événements de diagnostic
 */
class NotificationService {
  
  // --- COLLECTIONS FIRESTORE ---
  private diagnosticEventsCol = (userId: string) => 
    collection(db, 'users', userId, 'diagnostic_events');
  
  private notificationsCol = (userId: string) => 
    collection(db, 'users', userId, 'notifications');
  
  private actionHistoryCol = (userId: string) => 
    collection(db, 'users', userId, 'action_history');

  // --- ÉVÉNEMENTS DE DIAGNOSTIC ---

  /**
   * Crée un nouvel événement de diagnostic avec notification
   */
  async createDiagnosticEvent(eventData: CreateDiagnosticEventData): Promise<string> {
    const now = new Date();
    const diagnosticEvent: Omit<DiagnosticEvent, 'id'> = {
      ...eventData,
      dateCreated: Timestamp.fromDate(now),
      nextActionDate: Timestamp.fromDate(eventData.nextActionDate),
      priority: eventData.priority || 'medium',
      status: 'pending',
      completed: false,
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now)
    };

    const docRef = await addDoc(this.diagnosticEventsCol(eventData.userId), diagnosticEvent);
    
    // Créer une notification associée
    await this.createNotification({
      userId: eventData.userId,
      diagnosticEventId: docRef.id,
      title: `Rappel: ${eventData.title}`,
      message: `Action prévue le ${eventData.nextActionDate.toLocaleDateString()}: ${eventData.nextActionType}`,
      priority: eventData.priority || 'medium'
    });

    return docRef.id;
  }

  /**
   * Met à jour un événement de diagnostic
   */
  async updateDiagnosticEvent(
    userId: string, 
    eventId: string, 
    updateData: UpdateDiagnosticEventData
  ): Promise<void> {
    const eventRef = doc(db, 'users', userId, 'diagnostic_events', eventId);
    const updatePayload: any = {
      ...updateData,
      updatedAt: Timestamp.fromDate(new Date())
    };

    if (updateData.nextActionDate) {
      updatePayload.nextActionDate = Timestamp.fromDate(updateData.nextActionDate);
    }

    await updateDoc(eventRef, updatePayload);
  }

  /**
   * Marque un événement comme terminé
   */
  async completeEvent(userId: string, eventId: string): Promise<void> {
    await this.updateDiagnosticEvent(userId, eventId, {
      status: 'completed',
      completed: true
    });

    // Ajouter à l'historique des actions
    const event = await this.getDiagnosticEvent(userId, eventId);
    if (event) {
      await this.addActionHistory({
        userId,
        plantId: event.plantId,
        plantName: event.plantName,
        actionType: 'event_completed',
        description: `Événement terminé: ${event.title}`,
        metadata: { eventId, eventType: event.eventType }
      });
    }
  }

  /**
   * Récupère un événement de diagnostic
   */
  async getDiagnosticEvent(userId: string, eventId: string): Promise<DiagnosticEvent | null> {
    const eventRef = doc(db, 'users', userId, 'diagnostic_events', eventId);
    const eventSnap = await getDoc(eventRef);

    if (!eventSnap.exists()) {
      return null;
    }

    const data = eventSnap.data();
    return {
      id: eventSnap.id,
      ...data,
      dateCreated: data.dateCreated,
      nextActionDate: data.nextActionDate,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    } as DiagnosticEvent;
  }

  /**
   * Récupère les événements de diagnostic avec filtres
   */
  getDiagnosticEvents(
    userId: string,
    callback: (events: DiagnosticEvent[]) => void,
    filters?: DiagnosticEventFilters
  ): (() => void) {
    let q = query(this.diagnosticEventsCol(userId), orderBy('nextActionDate', 'asc'));

    // Application des filtres
    if (filters?.plantId) {
      q = query(q, where('plantId', '==', filters.plantId));
    }
    if (filters?.eventType) {
      q = query(q, where('eventType', '==', filters.eventType));
    }
    if (filters?.status) {
      q = query(q, where('status', '==', filters.status));
    }
    if (filters?.priority) {
      q = query(q, where('priority', '==', filters.priority));
    }
    if (filters?.pendingOnly) {
      q = query(q, where('completed', '==', false));
    }

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const eventsData = snapshot.docs.map(docSnap => ({
        id: docSnap.id,
        ...docSnap.data()
      } as DiagnosticEvent));
      callback(eventsData);
    });

    return unsubscribe;
  }

  // --- NOTIFICATIONS ---

  /**
   * Crée une nouvelle notification
   */
  async createNotification(notificationData: CreateNotificationData): Promise<string> {
    const now = new Date();
    const notification: Omit<UserNotification, 'id'> = {
      ...notificationData,
      read: false,
      createdAt: Timestamp.fromDate(now),
      // Ne pas inclure expiresAt si undefined - Firebase n'accepte pas les valeurs undefined
      ...(notificationData.expiresAt && { expiresAt: Timestamp.fromDate(notificationData.expiresAt) })
    };

    const docRef = await addDoc(this.notificationsCol(notificationData.userId), notification);
    return docRef.id;
  }

  /**
   * Marque une notification comme lue
   */
  async markNotificationAsRead(userId: string, notificationId: string): Promise<void> {
    const notificationRef = doc(db, 'users', userId, 'notifications', notificationId);
    await updateDoc(notificationRef, {
      read: true,
      readAt: Timestamp.fromDate(new Date())
    });
  }

  /**
   * Récupère les notifications d'un utilisateur
   */
  getUserNotifications(
    userId: string,
    callback: (notifications: UserNotification[]) => void,
    unreadOnly: boolean = false
  ): (() => void) {
    let q = query(this.notificationsCol(userId), orderBy('createdAt', 'desc'));
    
    if (unreadOnly) {
      q = query(q, where('read', '==', false));
    }

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const notificationsData = snapshot.docs.map(docSnap => ({
        id: docSnap.id,
        ...docSnap.data()
      } as UserNotification));
      callback(notificationsData);
    });

    return unsubscribe;
  }

  // --- HISTORIQUE DES ACTIONS ---

  /**
   * Ajoute une entrée à l'historique des actions
   */
  async addActionHistory(actionData: CreateActionHistoryData): Promise<string> {
    const now = new Date();
    const historyEntry: Omit<UserActionHistory, 'id'> = {
      ...actionData,
      actionDate: Timestamp.fromDate(now),
      createdAt: Timestamp.fromDate(now)
    };

    const docRef = await addDoc(this.actionHistoryCol(actionData.userId), historyEntry);
    return docRef.id;
  }

  /**
   * Récupère l'historique des actions d'un utilisateur
   */
  getActionHistory(
    userId: string,
    callback: (history: UserActionHistory[]) => void,
    plantId?: string
  ): (() => void) {
    let q = query(this.actionHistoryCol(userId), orderBy('actionDate', 'desc'));
    
    if (plantId) {
      q = query(q, where('plantId', '==', plantId));
    }

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const historyData = snapshot.docs.map(docSnap => ({
        id: docSnap.id,
        ...docSnap.data()
      } as UserActionHistory));
      callback(historyData);
    });

    return unsubscribe;
  }

  // --- STATISTIQUES ---

  /**
   * Calcule les statistiques de notifications pour un utilisateur
   */
  async getNotificationStats(userId: string): Promise<NotificationStats> {
    const [notificationsSnap, eventsSnap] = await Promise.all([
      getDocs(this.notificationsCol(userId)),
      getDocs(this.diagnosticEventsCol(userId))
    ]);

    const notifications = notificationsSnap.docs.map(doc => doc.data() as UserNotification);
    const events = eventsSnap.docs.map(doc => doc.data() as DiagnosticEvent);

    const unreadNotifications = notifications.filter(n => !n.read).length;
    const pendingEvents = events.filter(e => !e.completed).length;
    const overdueEvents = events.filter(e => 
      !e.completed && e.nextActionDate.toDate() < new Date()
    ).length;

    const notificationsByPriority = notifications.reduce((acc, n) => {
      acc[n.priority] = (acc[n.priority] || 0) + 1;
      return acc;
    }, {} as Record<NotificationPriority, number>);

    const eventsByType = events.reduce((acc, e) => {
      acc[e.eventType] = (acc[e.eventType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalNotifications: notifications.length,
      unreadNotifications,
      pendingEvents,
      overdueEvents,
      notificationsByPriority,
      eventsByType
    };
  }

  // --- INTÉGRATION AUTOMATIQUE ---

  /**
   * Crée automatiquement un événement de diagnostic et une entrée d'historique
   * à partir d'un diagnostic Gemini
   */
  async createDiagnosticEventFromGemini(
    userId: string,
    plantId: string,
    plantName: string,
    diagnosis: GeminiDiagnosis,
    previousDiagnostics?: DiagnosticRecord[]
  ): Promise<{ eventId: string; historyId: string }> {
    // Générer les recommandations intelligentes
    const recommendations = await this.generateSmartRecommendations(
      diagnosis,
      plantName,
      previousDiagnostics
    );

    // Créer l'événement de diagnostic
    const eventData: CreateDiagnosticEventData = {
      userId,
      plantId,
      plantName,
      title: diagnosis.isHealthy ?
        `✅ Plante en bonne santé - ${plantName}` :
        `⚠️ Problème détecté - ${plantName}`,
      description: diagnosis.isHealthy ?
        'Diagnostic positif. Continuez les soins actuels.' :
        `Problème détecté: ${diagnosis.issues.join(', ')}. ${recommendations.recommendation}`,
      eventType: 'diagnostic',
      nextActionDate: recommendations.nextActionDate,
      nextActionType: recommendations.nextActionType,
      priority: recommendations.priority,
      geminiRecommendation: recommendations.recommendation
    };

    const eventId = await this.createDiagnosticEvent(eventData);

    // Créer l'entrée d'historique
    const historyData: CreateActionHistoryData = {
      userId,
      plantId,
      plantName,
      actionType: 'diagnostic',
      description: diagnosis.isHealthy ?
        'Diagnostic effectué - Plante en bonne santé' :
        `Diagnostic effectué - Problèmes détectés: ${diagnosis.issues.join(', ')}`,
      metadata: {
        isHealthy: diagnosis.isHealthy,
        issues: diagnosis.issues,
        confidence: diagnosis.confidence,
        recommendations: diagnosis.recommendations,
        geminiRecommendation: recommendations.recommendation
      }
    };

    const historyId = await this.addActionHistory(historyData);

    return { eventId, historyId };
  }

  /**
   * Marque un événement comme terminé et crée une entrée d'historique
   */
  async completeEventWithHistory(
    userId: string,
    eventId: string,
    treatmentDescription?: string
  ): Promise<string> {
    // Récupérer l'événement
    const eventDoc = await getDoc(doc(this.diagnosticEventsCol(userId), eventId));
    if (!eventDoc.exists()) {
      throw new Error('Événement non trouvé');
    }

    const event = { id: eventDoc.id, ...eventDoc.data() } as DiagnosticEvent;

    // Marquer l'événement comme terminé
    await this.updateDiagnosticEvent(userId, eventId, {
      status: 'completed',
      completed: true
    });

    // Créer l'entrée d'historique
    const historyData: CreateActionHistoryData = {
      userId,
      plantId: event.plantId,
      plantName: event.plantName,
      actionType: treatmentDescription ? 'treatment_applied' : 'event_completed',
      description: treatmentDescription ||
        `Événement terminé: ${event.nextActionType} pour ${event.plantName}`,
      metadata: {
        eventId,
        eventType: event.eventType,
        originalAction: event.nextActionType,
        treatmentApplied: !!treatmentDescription
      }
    };

    return await this.addActionHistory(historyData);
  }

  // --- INTÉGRATION GEMINI ---

  /**
   * Génère des recommandations intelligentes basées sur un diagnostic
   */
  async generateSmartRecommendations(
    diagnosis: GeminiDiagnosis,
    plantName: string,
    previousDiagnostics?: DiagnosticRecord[]
  ): Promise<{
    nextActionDate: Date;
    nextActionType: string;
    recommendation: string;
    priority: NotificationPriority;
  }> {
    const now = new Date();
    const season = this.getCurrentSeason();
    const plantType = this.identifyPlantType(plantName);

    // Analyse de l'historique pour détecter les patterns
    const historyAnalysis = this.analyzeHistoryPatterns(previousDiagnostics);

    let nextActionDate = new Date(now);
    let nextActionType = '';
    let priority: NotificationPriority = 'medium';
    let recommendation = '';

    if (!diagnosis.isHealthy) {
      // Calculs sophistiqués pour plantes malades
      const diseaseAnalysis = this.analyzeDiseaseUrgency(diagnosis.disease);
      const treatmentFrequency = this.calculateTreatmentFrequency(
        diagnosis,
        season,
        plantType,
        historyAnalysis
      );

      nextActionDate.setDate(now.getDate() + treatmentFrequency);
      nextActionType = diseaseAnalysis.actionType;
      priority = diseaseAnalysis.priority;

      recommendation = this.generateTreatmentRecommendation(
        diagnosis,
        plantName,
        season,
        historyAnalysis
      );

    } else {
      // Calculs préventifs pour plantes saines
      const preventiveSchedule = this.calculatePreventiveSchedule(
        plantType,
        season,
        historyAnalysis
      );

      nextActionDate.setDate(now.getDate() + preventiveSchedule.days);
      nextActionType = preventiveSchedule.actionType;
      priority = preventiveSchedule.priority;

      recommendation = this.generatePreventiveRecommendation(
        plantName,
        season,
        historyAnalysis
      );
    }

    // Ajustements finaux basés sur l'apprentissage adaptatif
    const finalAdjustments = this.applyAdaptiveLearning(
      { nextActionDate, nextActionType, recommendation, priority },
      historyAnalysis
    );

    return finalAdjustments;
  }

  /**
   * Détermine la saison actuelle
   */
  private getCurrentSeason(): 'spring' | 'summer' | 'autumn' | 'winter' {
    const month = new Date().getMonth() + 1; // 1-12
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  /**
   * Identifie le type de plante pour adapter les soins
   */
  private identifyPlantType(plantName: string): 'indoor' | 'outdoor' | 'succulent' | 'tropical' | 'herb' | 'flower' | 'tree' {
    const name = plantName.toLowerCase();

    // Plantes d'intérieur
    if (name.includes('pothos') || name.includes('monstera') || name.includes('ficus') ||
        name.includes('philodendron') || name.includes('sansevieria')) {
      return 'indoor';
    }

    // Plantes grasses
    if (name.includes('cactus') || name.includes('succulent') || name.includes('aloe') ||
        name.includes('echeveria') || name.includes('jade')) {
      return 'succulent';
    }

    // Plantes tropicales
    if (name.includes('orchidée') || name.includes('anthurium') || name.includes('bromelia') ||
        name.includes('hibiscus')) {
      return 'tropical';
    }

    // Herbes aromatiques
    if (name.includes('basilic') || name.includes('menthe') || name.includes('thym') ||
        name.includes('romarin') || name.includes('persil')) {
      return 'herb';
    }

    // Arbres
    if (name.includes('arbre') || name.includes('bonsai') || name.includes('olivier') ||
        name.includes('citronnier')) {
      return 'tree';
    }

    // Fleurs
    if (name.includes('rose') || name.includes('tulipe') || name.includes('géranium') ||
        name.includes('pétunia')) {
      return 'flower';
    }

    return 'outdoor'; // Par défaut
  }

  /**
   * Analyse les patterns dans l'historique des diagnostics
   */
  private analyzeHistoryPatterns(previousDiagnostics?: DiagnosticRecord[]): {
    hasRecurrentIssues: boolean;
    averageHealthyPeriod: number;
    commonDiseases: string[];
    lastTreatmentSuccess: boolean;
    riskLevel: 'low' | 'medium' | 'high';
  } {
    if (!previousDiagnostics || previousDiagnostics.length === 0) {
      return {
        hasRecurrentIssues: false,
        averageHealthyPeriod: 30,
        commonDiseases: [],
        lastTreatmentSuccess: true,
        riskLevel: 'low'
      };
    }

    const recentDiagnostics = previousDiagnostics
      .filter(d => d.timestamp.toDate() > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)) // 90 jours
      .sort((a, b) => b.timestamp.toDate().getTime() - a.timestamp.toDate().getTime());

    const diseaseCount = recentDiagnostics.filter(d => !d.diagnosis.isHealthy).length;
    const hasRecurrentIssues = diseaseCount > 1;

    // Calcul de la période moyenne de bonne santé
    let healthyPeriods: number[] = [];
    for (let i = 0; i < recentDiagnostics.length - 1; i++) {
      if (recentDiagnostics[i].diagnosis.isHealthy && !recentDiagnostics[i + 1].diagnosis.isHealthy) {
        const period = Math.abs(
          recentDiagnostics[i].timestamp.toDate().getTime() -
          recentDiagnostics[i + 1].timestamp.toDate().getTime()
        ) / (24 * 60 * 60 * 1000);
        healthyPeriods.push(period);
      }
    }

    const averageHealthyPeriod = healthyPeriods.length > 0
      ? healthyPeriods.reduce((a, b) => a + b, 0) / healthyPeriods.length
      : 30;

    // Maladies communes
    const diseases = recentDiagnostics
      .filter(d => !d.diagnosis.isHealthy)
      .map(d => d.diagnosis.disease);
    const commonDiseases = [...new Set(diseases)];

    // Succès du dernier traitement
    const lastTreatmentSuccess = recentDiagnostics.length >= 2
      ? recentDiagnostics[0].diagnosis.isHealthy && !recentDiagnostics[1].diagnosis.isHealthy
      : true;

    // Niveau de risque
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    if (diseaseCount >= 3 || averageHealthyPeriod < 14) riskLevel = 'high';
    else if (diseaseCount >= 2 || averageHealthyPeriod < 21) riskLevel = 'medium';

    return {
      hasRecurrentIssues,
      averageHealthyPeriod,
      commonDiseases,
      lastTreatmentSuccess,
      riskLevel
    };
  }

  /**
   * Analyse l'urgence d'une maladie
   */
  private analyzeDiseaseUrgency(disease: string): {
    actionType: string;
    priority: NotificationPriority;
    urgencyScore: number;
  } {
    const diseaseLower = disease.toLowerCase();

    // Maladies critiques (urgence maximale)
    if (diseaseLower.includes('pourriture') || diseaseLower.includes('flétrissement') ||
        diseaseLower.includes('nécrose') || diseaseLower.includes('chancre')) {
      return {
        actionType: 'Intervention d\'urgence',
        priority: 'urgent',
        urgencyScore: 10
      };
    }

    // Maladies graves (haute priorité)
    if (diseaseLower.includes('mildiou') || diseaseLower.includes('oïdium') ||
        diseaseLower.includes('rouille') || diseaseLower.includes('anthracnose')) {
      return {
        actionType: 'Traitement immédiat',
        priority: 'high',
        urgencyScore: 8
      };
    }

    // Parasites (priorité moyenne-haute)
    if (diseaseLower.includes('puceron') || diseaseLower.includes('cochenille') ||
        diseaseLower.includes('araignée') || diseaseLower.includes('thrips')) {
      return {
        actionType: 'Traitement antiparasitaire',
        priority: 'high',
        urgencyScore: 7
      };
    }

    // Carences nutritionnelles (priorité moyenne)
    if (diseaseLower.includes('carence') || diseaseLower.includes('chlorose') ||
        diseaseLower.includes('jaunissement')) {
      return {
        actionType: 'Correction nutritionnelle',
        priority: 'medium',
        urgencyScore: 5
      };
    }

    // Problèmes environnementaux (priorité faible-moyenne)
    if (diseaseLower.includes('stress') || diseaseLower.includes('brûlure') ||
        diseaseLower.includes('dessèchement')) {
      return {
        actionType: 'Ajustement des conditions',
        priority: 'medium',
        urgencyScore: 4
      };
    }

    // Cas général
    return {
      actionType: 'Vérification du traitement',
      priority: 'medium',
      urgencyScore: 5
    };
  }

  /**
   * Calcule la fréquence de traitement optimale
   */
  private calculateTreatmentFrequency(
    diagnosis: GeminiDiagnosis,
    season: string,
    plantType: string,
    historyAnalysis: any
  ): number {
    let baseDays = diagnosis.treatmentPlan.treatmentFrequencyDays || 7;

    // Ajustement selon la saison
    const seasonMultiplier = {
      'spring': 0.8,  // Croissance active, traitement plus fréquent
      'summer': 1.0,  // Période normale
      'autumn': 1.2,  // Ralentissement, moins fréquent
      'winter': 1.5   // Dormance, beaucoup moins fréquent
    };

    baseDays *= seasonMultiplier[season as keyof typeof seasonMultiplier] || 1.0;

    // Ajustement selon le type de plante
    const plantMultiplier = {
      'succulent': 1.5,  // Moins d'interventions
      'tropical': 0.8,   // Plus d'attention
      'herb': 0.9,       // Croissance rapide
      'indoor': 1.1,     // Environnement contrôlé
      'outdoor': 1.0,    // Standard
      'flower': 0.9,     // Période de floraison
      'tree': 1.3        // Croissance lente
    };

    baseDays *= plantMultiplier[plantType as keyof typeof plantMultiplier] || 1.0;

    // Ajustement selon l'historique
    if (historyAnalysis.hasRecurrentIssues) {
      baseDays *= 0.7; // Plus fréquent si problèmes récurrents
    }

    if (historyAnalysis.riskLevel === 'high') {
      baseDays *= 0.6;
    } else if (historyAnalysis.riskLevel === 'medium') {
      baseDays *= 0.8;
    }

    // Limites raisonnables
    return Math.max(1, Math.min(30, Math.round(baseDays)));
  }

  /**
   * Calcule le planning préventif pour plantes saines
   */
  private calculatePreventiveSchedule(
    plantType: string,
    season: string,
    historyAnalysis: any
  ): { days: number; actionType: string; priority: NotificationPriority } {
    let baseDays = 14; // 2 semaines par défaut

    // Ajustement selon le type de plante
    const plantSchedule = {
      'succulent': { days: 21, action: 'Contrôle hydrique' },
      'tropical': { days: 10, action: 'Vérification humidité' },
      'herb': { days: 7, action: 'Récolte et taille' },
      'indoor': { days: 14, action: 'Contrôle général' },
      'outdoor': { days: 14, action: 'Inspection extérieure' },
      'flower': { days: 10, action: 'Suivi floraison' },
      'tree': { days: 28, action: 'Inspection arbre' }
    };

    const schedule = plantSchedule[plantType as keyof typeof plantSchedule] ||
                    { days: 14, action: 'Contrôle préventif' };

    baseDays = schedule.days;

    // Ajustement saisonnier
    if (season === 'spring') baseDays *= 0.8; // Plus de surveillance au printemps
    if (season === 'winter') baseDays *= 1.5; // Moins en hiver

    // Ajustement selon l'historique
    if (historyAnalysis.riskLevel === 'high') {
      baseDays *= 0.7;
    } else if (historyAnalysis.riskLevel === 'medium') {
      baseDays *= 0.9;
    }

    const priority: NotificationPriority = historyAnalysis.riskLevel === 'high' ? 'medium' : 'low';

    return {
      days: Math.max(3, Math.min(60, Math.round(baseDays))),
      actionType: schedule.action,
      priority
    };
  }

  /**
   * Génère une recommandation de traitement personnalisée
   */
  private generateTreatmentRecommendation(
    diagnosis: GeminiDiagnosis,
    plantName: string,
    season: string,
    historyAnalysis: any
  ): string {
    let recommendation = `🌱 Traitement pour ${plantName} - ${diagnosis.disease}\n\n`;

    // Étapes du traitement
    recommendation += `📋 Plan d'action :\n`;
    diagnosis.treatmentPlan.steps.forEach((step, index) => {
      recommendation += `${index + 1}. ${step}\n`;
    });

    // Conseils saisonniers
    const seasonalTips = {
      'spring': 'Profitez de la reprise de croissance pour un traitement efficace.',
      'summer': 'Attention à la chaleur, traitez de préférence le matin ou le soir.',
      'autumn': 'Préparez la plante pour l\'hiver après le traitement.',
      'winter': 'Réduisez les interventions, la plante est en dormance.'
    };

    recommendation += `\n🌤️ Conseil saisonnier : ${seasonalTips[season as keyof typeof seasonalTips]}\n`;

    // Alertes basées sur l'historique
    if (historyAnalysis.hasRecurrentIssues) {
      recommendation += `\n⚠️ Attention : Cette plante a eu des problèmes récurrents. Surveillez attentivement l'évolution.`;
    }

    if (historyAnalysis.commonDiseases.includes(diagnosis.disease)) {
      recommendation += `\n🔄 Cette maladie est déjà apparue. Vérifiez les conditions environnementales.`;
    }

    return recommendation;
  }

  /**
   * Génère une recommandation préventive
   */
  private generatePreventiveRecommendation(
    plantName: string,
    season: string,
    historyAnalysis: any
  ): string {
    let recommendation = `✅ ${plantName} est en excellente santé !\n\n`;

    // Conseils préventifs saisonniers
    const preventiveTips = {
      'spring': '🌱 Période de croissance : augmentez l\'arrosage et la fertilisation.',
      'summer': '☀️ Protégez du soleil intense et maintenez l\'humidité.',
      'autumn': '🍂 Réduisez progressivement l\'arrosage et préparez l\'hivernage.',
      'winter': '❄️ Période de repos : arrosage minimal et pas d\'engrais.'
    };

    recommendation += preventiveTips[season as keyof typeof preventiveTips] + '\n\n';

    // Conseils basés sur l'historique
    if (historyAnalysis.riskLevel === 'high') {
      recommendation += `🔍 Surveillance renforcée recommandée en raison de l'historique.`;
    } else if (historyAnalysis.commonDiseases.length > 0) {
      recommendation += `💡 Restez vigilant aux signes de : ${historyAnalysis.commonDiseases.join(', ')}.`;
    } else {
      recommendation += `🎉 Continuez vos excellents soins !`;
    }

    return recommendation;
  }

  /**
   * Applique l'apprentissage adaptatif aux recommandations
   */
  private applyAdaptiveLearning(
    baseRecommendation: {
      nextActionDate: Date;
      nextActionType: string;
      recommendation: string;
      priority: NotificationPriority;
    },
    historyAnalysis: any
  ): {
    nextActionDate: Date;
    nextActionType: string;
    recommendation: string;
    priority: NotificationPriority;
  } {
    let adjustedRecommendation = { ...baseRecommendation };

    // Si le dernier traitement a échoué, raccourcir le délai
    if (!historyAnalysis.lastTreatmentSuccess && historyAnalysis.hasRecurrentIssues) {
      const currentDays = Math.ceil(
        (adjustedRecommendation.nextActionDate.getTime() - new Date().getTime()) / (24 * 60 * 60 * 1000)
      );
      const newDays = Math.max(1, Math.round(currentDays * 0.6));

      adjustedRecommendation.nextActionDate = new Date();
      adjustedRecommendation.nextActionDate.setDate(new Date().getDate() + newDays);

      adjustedRecommendation.recommendation += `\n\n🤖 IA adaptative : Délai raccourci en raison des difficultés précédentes.`;
    }

    // Ajustement de priorité basé sur l'apprentissage
    if (historyAnalysis.riskLevel === 'high' && adjustedRecommendation.priority === 'medium') {
      adjustedRecommendation.priority = 'high';
      adjustedRecommendation.recommendation += `\n\n📈 Priorité augmentée par l'IA en raison du profil de risque.`;
    }

    return adjustedRecommendation;
  }

  /**
   * Crée automatiquement un événement de diagnostic basé sur un diagnostic Gemini
   */
  async createEventFromDiagnosis(
    userId: string,
    plantId: string,
    plantName: string,
    diagnosis: GeminiDiagnosis,
    diagnosticId: string,
    previousDiagnostics?: DiagnosticRecord[]
  ): Promise<string> {
    const recommendations = await this.generateSmartRecommendations(
      diagnosis,
      plantName,
      previousDiagnostics
    );

    const eventData: CreateDiagnosticEventData = {
      userId,
      plantId,
      plantName,
      eventType: diagnosis.isHealthy ? 'suivi' : 'traitement',
      treatmentType: diagnosis.isHealthy ? 'arrosage' : 'traitement',
      title: diagnosis.isHealthy
        ? `Contrôle préventif - ${plantName}`
        : `Traitement ${diagnosis.disease} - ${plantName}`,
      description: diagnosis.description,
      nextActionDate: recommendations.nextActionDate,
      nextActionType: recommendations.nextActionType,
      geminiRecommendation: recommendations.recommendation,
      priority: recommendations.priority,
      originalDiagnosticId: diagnosticId
    };

    const eventId = await this.createDiagnosticEvent(eventData);

    // Ajouter à l'historique
    await this.addActionHistory({
      userId,
      plantId,
      plantName,
      actionType: 'diagnostic',
      description: `Diagnostic effectué: ${diagnosis.disease}`,
      metadata: {
        diagnosticId,
        eventId,
        isHealthy: diagnosis.isHealthy,
        disease: diagnosis.disease
      }
    });

    return eventId;
  }

  /**
   * Vérifie les événements en retard et crée des alertes
   */
  async checkOverdueEvents(userId: string): Promise<void> {
    const eventsSnap = await getDocs(
      query(
        this.diagnosticEventsCol(userId),
        where('completed', '==', false),
        where('nextActionDate', '<', Timestamp.fromDate(new Date()))
      )
    );

    for (const eventDoc of eventsSnap.docs) {
      const event = { id: eventDoc.id, ...eventDoc.data() } as DiagnosticEvent;

      // Mettre à jour le statut
      await this.updateDiagnosticEvent(userId, event.id, { status: 'overdue' });

      // Créer une notification d'alerte
      await this.createNotification({
        userId,
        diagnosticEventId: event.id,
        title: `⚠️ Action en retard - ${event.plantName}`,
        message: `L'action "${event.nextActionType}" était prévue le ${event.nextActionDate.toDate().toLocaleDateString()}. Veuillez intervenir rapidement.`,
        priority: 'urgent'
      });
    }
  }

  // --- NOTIFICATIONS PRÉVENTIVES ---

  /**
   * Génère des notifications préventives basées sur l'analyse prédictive
   */
  async generatePreventiveNotifications(userId: string): Promise<void> {
    try {
      console.log(`🔍 Début de l'analyse préventive pour l'utilisateur ${userId}`);

      // Récupérer toutes les plantes de l'utilisateur
      const plantsSnapshot = await getDocs(collection(db, 'users', userId, 'plants'));
      const plants = plantsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      console.log(`📊 ${plants.length} plante(s) trouvée(s) pour analyse`);

      let notificationsCreated = 0;
      for (const plant of plants) {
        const created = await this.analyzeAndCreatePreventiveNotifications(userId, plant);
        if (created) notificationsCreated++;
      }

      console.log(`✅ Analyse terminée: ${notificationsCreated} nouvelle(s) notification(s) préventive(s) créée(s)`);
    } catch (error) {
      console.error('Erreur lors de la génération des notifications préventives:', error);
    }
  }

  /**
   * Analyse une plante et crée des notifications préventives si nécessaire
   */
  private async analyzeAndCreatePreventiveNotifications(userId: string, plant: any): Promise<boolean> {
    try {
      // Récupérer l'historique des diagnostics depuis la bonne structure
      // Structure correcte: users/{userId}/plants/{plantId}/diagnostics
      const diagnosticsSnapshot = await getDocs(
        query(
          collection(db, 'users', userId, 'plants', plant.id, 'diagnostics'),
          orderBy('timestamp', 'desc')
        )
      );

      const diagnostics = diagnosticsSnapshot.docs.map(doc => ({
        id: doc.id,
        plantId: plant.id, // Ajouter plantId pour cohérence
        ...doc.data()
      })) as DiagnosticRecord[];

      // Analyser les patterns et risques (déjà triés par orderBy)
      const riskAnalysis = this.analyzePreventiveRisks(diagnostics, plant);

      // Créer des notifications préventives si nécessaire
      if (riskAnalysis.shouldNotify) {
        const created = await this.createPreventiveNotification(userId, plant, riskAnalysis);
        return created;
      }

      return false;
    } catch (error) {
      console.warn(`⚠️ Impossible d'analyser les notifications préventives pour la plante ${plant.id}:`, error);
      // Ne pas faire échouer toute la génération pour une plante
      return false;
    }
  }

  /**
   * Analyse les risques préventifs pour une plante
   */
  private analyzePreventiveRisks(diagnostics: DiagnosticRecord[], plant: any): {
    shouldNotify: boolean;
    riskLevel: 'low' | 'medium' | 'high';
    riskFactors: string[];
    recommendedActions: string[];
    priority: NotificationPriority;
  } {
    const now = new Date();
    const season = this.getCurrentSeason();
    const plantType = this.identifyPlantType(plant.name);

    let riskFactors: string[] = [];
    let recommendedActions: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // 1. Analyse temporelle - pas de diagnostic récent
    const lastDiagnostic = diagnostics[0];
    const daysSinceLastCheck = lastDiagnostic
      ? Math.floor((now.getTime() - lastDiagnostic.timestamp.toDate().getTime()) / (24 * 60 * 60 * 1000))
      : 999;

    if (daysSinceLastCheck > 30) {
      riskFactors.push('Aucun diagnostic depuis plus de 30 jours');
      recommendedActions.push('Effectuer un diagnostic de contrôle');
      riskLevel = 'medium';
    }

    // 2. Analyse saisonnière - risques spécifiques
    const seasonalRisks = this.getSeasonalRisks(season, plantType);
    if (seasonalRisks.length > 0) {
      riskFactors.push(...seasonalRisks.map(r => `Risque saisonnier: ${r.risk}`));
      recommendedActions.push(...seasonalRisks.map(r => r.action));
      if (seasonalRisks.some(r => r.severity === 'high')) {
        riskLevel = 'high';
      } else if (riskLevel === 'low') {
        riskLevel = 'medium';
      }
    }

    // 3. Analyse de l'historique - patterns de maladies
    const historyAnalysis = this.analyzeHistoryPatterns(diagnostics);
    if (historyAnalysis.hasRecurrentIssues) {
      riskFactors.push('Historique de problèmes récurrents');
      recommendedActions.push('Surveillance renforcée recommandée');
      riskLevel = 'high';
    }

    // 4. Prédiction basée sur les cycles
    const cyclePrediction = this.predictDiseaseRisk(diagnostics, season);
    if (cyclePrediction.riskScore > 0.7) {
      riskFactors.push(`Risque élevé de ${cyclePrediction.likelyDisease}`);
      recommendedActions.push(cyclePrediction.preventiveAction);
      riskLevel = 'high';
    }

    const shouldNotify = riskFactors.length > 0;
    const priority: NotificationPriority = riskLevel === 'high' ? 'high' :
                                          riskLevel === 'medium' ? 'medium' : 'low';

    return {
      shouldNotify,
      riskLevel,
      riskFactors,
      recommendedActions,
      priority
    };
  }

  /**
   * Obtient les risques saisonniers spécifiques
   */
  private getSeasonalRisks(season: string, plantType: string): Array<{
    risk: string;
    action: string;
    severity: 'low' | 'medium' | 'high';
  }> {
    const risks: Array<{ risk: string; action: string; severity: 'low' | 'medium' | 'high' }> = [];

    if (season === 'spring') {
      risks.push(
        { risk: 'pucerons avec la reprise de croissance', action: 'Inspecter les nouvelles pousses', severity: 'medium' },
        { risk: 'champignons avec l\'humidité', action: 'Améliorer la ventilation', severity: 'medium' }
      );
    } else if (season === 'summer') {
      risks.push(
        { risk: 'stress hydrique et brûlures', action: 'Vérifier l\'arrosage et l\'ombrage', severity: 'high' },
        { risk: 'araignées rouges par temps sec', action: 'Augmenter l\'humidité ambiante', severity: 'medium' }
      );
    } else if (season === 'autumn') {
      risks.push(
        { risk: 'pourriture avec l\'humidité', action: 'Réduire l\'arrosage progressivement', severity: 'high' },
        { risk: 'chute naturelle des feuilles', action: 'Nettoyer les feuilles mortes', severity: 'low' }
      );
    } else if (season === 'winter') {
      risks.push(
        { risk: 'excès d\'eau en dormance', action: 'Espacer les arrosages', severity: 'medium' },
        { risk: 'manque de lumière', action: 'Rapprocher d\'une fenêtre', severity: 'low' }
      );
    }

    // Risques spécifiques au type de plante
    if (plantType === 'succulent' && (season === 'autumn' || season === 'winter')) {
      risks.push({ risk: 'pourriture des racines', action: 'Arroser très peu', severity: 'high' });
    }

    return risks;
  }

  /**
   * Prédit le risque de maladie basé sur les cycles historiques
   */
  private predictDiseaseRisk(diagnostics: DiagnosticRecord[], season: string): {
    riskScore: number;
    likelyDisease: string;
    preventiveAction: string;
  } {
    if (diagnostics.length < 2) {
      return { riskScore: 0, likelyDisease: '', preventiveAction: '' };
    }

    // Analyser les maladies par saison dans l'historique
    const seasonalDiseases = diagnostics
      .filter(d => !d.diagnosis.isHealthy)
      .filter(d => {
        const diagSeason = this.getSeasonFromDate(d.timestamp.toDate());
        return diagSeason === season;
      });

    if (seasonalDiseases.length === 0) {
      return { riskScore: 0, likelyDisease: '', preventiveAction: '' };
    }

    // Maladie la plus fréquente cette saison
    const diseaseFrequency: { [key: string]: number } = {};
    seasonalDiseases.forEach(d => {
      diseaseFrequency[d.diagnosis.disease] = (diseaseFrequency[d.diagnosis.disease] || 0) + 1;
    });

    const mostCommonDisease = Object.entries(diseaseFrequency)
      .sort(([,a], [,b]) => b - a)[0];

    if (!mostCommonDisease) {
      return { riskScore: 0, likelyDisease: '', preventiveAction: '' };
    }

    const [disease, frequency] = mostCommonDisease;
    const riskScore = Math.min(frequency / diagnostics.length * 2, 1); // Max 1.0

    return {
      riskScore,
      likelyDisease: disease,
      preventiveAction: `Appliquer un traitement préventif contre ${disease}`
    };
  }

  /**
   * Détermine la saison à partir d'une date
   */
  private getSeasonFromDate(date: Date): string {
    const month = date.getMonth() + 1;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  /**
   * Crée une notification préventive (avec vérification de doublons)
   */
  private async createPreventiveNotification(
    userId: string,
    plant: any,
    riskAnalysis: any
  ): Promise<boolean> {
    const title = `🔮 Prédiction IA - ${plant.name}`;
    const message = `Risques détectés: ${riskAnalysis.riskFactors.join(', ')}. Actions recommandées: ${riskAnalysis.recommendedActions.join(', ')}.`;

    // Vérifier s'il existe déjà une notification préventive pour cette plante (dernières 72h pour être plus strict)
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

    // Recherche spécifique : notifications préventives pour cette plante
    const existingNotificationsSnap = await getDocs(
      query(
        this.notificationsCol(userId),
        where('createdAt', '>', Timestamp.fromDate(threeDaysAgo)),
        orderBy('createdAt', 'desc')
      )
    );

    // Vérifier manuellement s'il y a une notification préventive pour cette plante
    const recentPreventiveNotifications = existingNotificationsSnap.docs.filter(doc => {
      const notification = doc.data();
      return notification.title &&
             notification.title.includes('🔮 Prédiction IA') &&
             notification.title.includes(plant.name);
    });

    if (recentPreventiveNotifications.length > 0) {
      const lastNotification = recentPreventiveNotifications[0];
      const lastNotificationDate = lastNotification.data().createdAt.toDate();
      const hoursSinceLastNotification = (new Date().getTime() - lastNotificationDate.getTime()) / (1000 * 60 * 60);

      console.log(`⚠️ Notification préventive déjà existante pour ${plant.name} il y a ${Math.round(hoursSinceLastNotification)}h, évitement du doublon`);
      return false;
    }

    // Vérifier aussi s'il y a trop de notifications préventives en général (max 1 par jour)
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    const todayPreventiveNotificationsSnap = await getDocs(
      query(
        this.notificationsCol(userId),
        where('createdAt', '>', Timestamp.fromDate(todayStart))
      )
    );

    const todayPreventiveCount = todayPreventiveNotificationsSnap.docs.filter(doc => {
      const notification = doc.data();
      return notification.title && notification.title.includes('🔮 Prédiction IA');
    }).length;

    if (todayPreventiveCount >= 1) { // Réduire drastiquement à max 1 notification préventive par jour
      console.log(`⚠️ Limite quotidienne de notifications préventives atteinte (${todayPreventiveCount}/1), évitement du spam`);
      return false;
    }

    console.log(`✅ Création d'une nouvelle notification préventive pour ${plant.name} (${todayPreventiveCount + 1}/1 aujourd'hui)`);
    await this.createNotification({
      userId,
      diagnosticEventId: '', // Pas d'événement spécifique
      title,
      message,
      priority: riskAnalysis.priority
    });

    return true;
  }
}

// Instance singleton du service
export const notificationService = new NotificationService();
